---
obsidianUIMode: preview
---

您的软件测试开发工作中心。这里是您的**主页**。

> [!Map]- # 🗺️ 知识地图 (Atlas)
> > *您想要去哪里？*
>
> - 📋 **测试管理**: [[测试项目管理]] | [[测试计划模板]] | [[缺陷管理]]
> - 🔧 **技术工具**: [[自动化测试框架]] | [[测试工具库]] | [[代码审查指南]]
> - 📚 **知识库**: [[测试技术文档]] | [[最佳实践]] | [[学习笔记]]
> - 👥 **团队协作**: [[团队成员]] | [[会议记录]] | [[知识分享]]
> - 🎯 **个人发展**: [[技能提升计划]] | [[认证学习]] | [[职业规划]]

> [!Calendar]- # 📅 日程管理 (Calendar)
> > *今天要做什么？*
>
> - 📝 **每日记录**: 按 `Cmd-d` 或 `Ctrl-d` 创建今日笔记
> - 📊 **测试日志**: [[测试执行日志]] | [[缺陷发现记录]] | [[性能测试记录]]
> - 📋 **工作计划**: [[每周测试计划]] | [[Sprint回顾]] | [[里程碑跟踪]]
> - 🔄 **定期回顾**: [[月度总结]] | [[季度规划]] | [[年度目标]]

> [!Training]- # 🎯 工作项目 (Efforts)
> > *当前在进行什么工作？*
>
> 查看完整视图，请访问 [[Efforts]]。
>
> 使用此功能保持优先级顺序，并根据需要快速调整工作带宽。
>
> > [!Box]+ ### 🔥 正在进行 (On)
> > ``` dataview
> > TABLE WITHOUT ID
> > file.link as "项目",
> > rank as "优先级"
> > FROM "Efforts/On"
> > SORT rank desc
> > ```
>
> > [!Box]+ ### ♻️ 持续进行 (Ongoing)
> > ``` dataview
> > TABLE WITHOUT ID
> > file.link as "项目",
> > rank as "优先级"
> > FROM "Efforts/Ongoing"
> > SORT rank desc
> > ```
>
> > [!Box]- ### 〰️ 后台进行 (Simmering)
> > 项目可以轻松从"正在进行"转移到"后台进行"状态。
> >
> > ``` dataview
> > TABLE WITHOUT ID
> > file.link as "项目",
> > rank as "优先级"
> > FROM "Efforts/Simmering"
> > SORT rank desc
> > ```

## 🚀 快速访问

### 📋 常用模板
- [[测试计划模板]] - 创建新的测试计划
- [[缺陷报告模板]] - 记录发现的缺陷
- [[每日测试工作记录]] - 记录每日工作内容

### 🔧 实用工具
- [[测试工具库]] - 查看和选择测试工具
- [[自动化测试框架]] - 自动化测试相关资源
- [[团队协作]] - 团队沟通和协作信息

### 📊 项目看板
- [[Efforts]] - 查看所有工作项目
- [[测试项目管理]] - 测试项目管理中心

---

> [!tip] 💡 使用提示
> - 按 `Cmd+P` (Mac) 或 `Ctrl+P` (Windows) 快速搜索文件
> - 按 `Cmd+D` (Mac) 或 `Ctrl+D` (Windows) 创建每日笔记
> - 使用 `[[]]` 创建链接到其他笔记
> - 使用标签 `#标签名` 来分类和组织笔记

![[pale-blue-dot-banner.jpg]]