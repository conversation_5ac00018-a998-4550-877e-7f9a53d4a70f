---
up: []
related: []
created: {{date}}
bug_id: 
severity: 
priority: 
status: 新建
assignee: 
tags: [缺陷, bug]
---

# 🐛 缺陷报告 - {{title}}

## 📋 基本信息

| 字段 | 值 |
|------|---|
| **缺陷ID** | {{bug_id}} |
| **标题** | {{title}} |
| **发现人** | {{发现人}} |
| **发现日期** | {{date}} |
| **严重程度** | {{severity}} |
| **优先级** | {{priority}} |
| **状态** | {{status}} |
| **指派给** | {{assignee}} |
| **所属模块** | {{模块}} |
| **版本** | {{version}} |
| **环境** | {{environment}} |

## 🎯 缺陷描述

### 简要描述
{{简要描述缺陷现象}}

### 详细描述
{{详细描述缺陷的具体表现、影响范围等}}

## 🔄 重现步骤

### 前置条件
- [ ] {{前置条件1}}
- [ ] {{前置条件2}}
- [ ] {{前置条件3}}

### 操作步骤
1. {{步骤1}}
2. {{步骤2}}
3. {{步骤3}}
4. {{步骤4}}

### 实际结果
{{描述实际看到的结果}}

### 期望结果
{{描述应该看到的正确结果}}

## 📱 测试环境

### 硬件环境
- **设备型号**: {{设备型号}}
- **屏幕分辨率**: {{分辨率}}
- **内存**: {{内存大小}}

### 软件环境
- **操作系统**: {{操作系统及版本}}
- **浏览器**: {{浏览器及版本}}
- **应用版本**: {{应用版本号}}
- **数据库**: {{数据库版本}}

## 📊 严重程度分级

> [!info]- 严重程度说明
> - **致命 (Critical)**: 系统崩溃、数据丢失、安全漏洞
> - **严重 (High)**: 主要功能无法使用、严重性能问题
> - **一般 (Medium)**: 次要功能异常、轻微性能问题
> - **轻微 (Low)**: 界面问题、文字错误、建议改进

## 📎 附件

### 截图
- [ ] 错误截图1: {{截图描述}}
- [ ] 错误截图2: {{截图描述}}

### 日志文件
- [ ] 应用日志: {{日志文件路径}}
- [ ] 系统日志: {{日志文件路径}}
- [ ] 网络日志: {{日志文件路径}}

### 视频录制
- [ ] 操作录屏: {{视频文件路径}}

## 🔍 根因分析

### 可能原因
- [ ] 代码逻辑错误
- [ ] 数据问题
- [ ] 环境配置问题
- [ ] 第三方依赖问题
- [ ] 网络问题

### 影响范围
- **功能影响**: {{描述对功能的影响}}
- **用户影响**: {{描述对用户的影响}}
- **业务影响**: {{描述对业务的影响}}

## 🛠️ 修复建议

### 临时解决方案
{{如果有临时绕过方案，请描述}}

### 建议修复方案
{{建议的修复方法或思路}}

## 📝 处理记录

### 开发团队反馈
| 日期 | 处理人 | 状态变更 | 备注 |
|------|--------|---------|------|
| {{date}} | {{处理人}} | 新建 → 确认 | {{备注}} |
| | | | |

### 测试验证记录
| 日期 | 验证人 | 验证结果 | 备注 |
|------|--------|---------|------|
| {{date}} | {{验证人}} | {{通过/失败}} | {{备注}} |
| | | | |

## 🔄 状态流转

```mermaid
stateDiagram-v2
    [*] --> 新建
    新建 --> 确认
    新建 --> 拒绝
    确认 --> 修复中
    修复中 --> 待验证
    待验证 --> 已关闭
    待验证 --> 重新打开
    重新打开 --> 修复中
    拒绝 --> [*]
    已关闭 --> [*]
```

## 📋 检查清单

### 缺陷报告质量检查
- [ ] 标题简洁明确
- [ ] 重现步骤清晰完整
- [ ] 实际结果和期望结果明确
- [ ] 严重程度和优先级合理
- [ ] 附件完整（截图、日志等）
- [ ] 环境信息详细

### 验证检查
- [ ] 在相同环境下可重现
- [ ] 在不同环境下验证
- [ ] 回归测试相关功能
- [ ] 确认修复不影响其他功能

## 📊 相关指标

- **发现阶段**: {{单元测试/集成测试/系统测试/用户测试}}
- **修复时间**: {{预估修复时间}}
- **验证时间**: {{预估验证时间}}
- **关联缺陷**: {{相关缺陷ID}}

---

**创建时间**: {{date}}  
**最后更新**: {{date}}  
**当前状态**: {{status}}

返回 [[测试项目管理]] | [[缺陷跟踪看板]]
